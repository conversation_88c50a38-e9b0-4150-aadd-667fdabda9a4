import 'package:flutter_test/flutter_test.dart';
import 'package:coffee_cofe/database/app_database.dart';
import 'package:coffee_cofe/database/entities/gst_configuration_dto.dart';
import 'package:coffee_cofe/database/entities/sales_transaction_dto.dart';
import 'package:coffee_cofe/services/gst_service.dart';
import 'package:coffee_cofe/services/simple_gst_service.dart';
import 'package:coffee_cofe/features/settings/settings_provider.dart';
import 'package:coffee_cofe/features/bill/billing_screen_provider.dart';

void main() {
  group('GST Integration Tests', () {
    late AppDatabase database;
    late GSTService gstService;
    late SimpleGSTService simpleGstService;
    late SettingsProvider settingsProvider;
    late BillingScreenProvider billingProvider;

    setUpAll(() async {
      database = AppDatabase();
      gstService = GSTService();
      simpleGstService = SimpleGSTService();
      settingsProvider = SettingsProvider();
      billingProvider = BillingScreenProvider();
      
      await settingsProvider.initProviderFunction();
      await billingProvider.iniFunction();
    });

    test('GST Configuration should save and load correctly', () async {
      final dao = await database.gstConfigurationDao;
      
      final config = GSTConfigurationDto(
        workSpaceId: 'test_workspace',
        businessName: 'Test Coffee Shop',
        businessGSTIN: '33AAAAA0000A1Z5',
        businessStateCode: '33',
        businessAddress: '123 Test Street, Test City',
        defaultGSTRate: 18.0,
        enableIGST: true,
        enableCess: false,
        enableReverseCharge: false,
        taxInclusivePricing: false,
        roundingMethod: 'ROUND_OFF',
        isActive: true,
        syncStatus: 0,
      );

      // Save configuration
      final id = await dao.insertOrUpdate(config);
      expect(id, greaterThan(0));

      // Load configuration
      final loadedConfig = await dao.getActiveConfiguration('test_workspace');
      expect(loadedConfig, isNotNull);
      expect(loadedConfig!.businessName, equals('Test Coffee Shop'));
      expect(loadedConfig.defaultGSTRate, equals(18.0));
      expect(loadedConfig.enableIGST, isTrue);
    });

    test('GST Service should calculate tax correctly', () async {
      // Enable tax in settings
      settingsProvider.toggleTax(true);
      
      // Create sample cart items
      final cartItems = [
        SalesTransactionDto(
          id: 1,
          productId: 'product_1',
          productName: 'Coffee',
          productAmount: 100.0,
          quantity: 2,
          unitPrice: 50.0,
          salesId: 'test_sale',
          workSpaceId: 'test_workspace',
          syncStatus: 0,
        ),
        SalesTransactionDto(
          id: 2,
          productId: 'product_2',
          productName: 'Tea',
          productAmount: 60.0,
          quantity: 1,
          unitPrice: 60.0,
          salesId: 'test_sale',
          workSpaceId: 'test_workspace',
          syncStatus: 0,
        ),
      ];

      // Calculate GST
      final gstCalculation = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 0.0,
        settingsProvider: settingsProvider,
      );

      expect(gstCalculation.subtotal, equals(160.0));
      expect(gstCalculation.totalGST, greaterThan(0));
      expect(gstCalculation.grandTotal, greaterThan(160.0));
    });

    test('Simple GST Service should work with settings provider', () async {
      // Enable tax in settings
      settingsProvider.toggleTax(true);
      
      // Create sample cart items
      final cartItems = [
        SalesTransactionDto(
          id: 1,
          productId: 'product_1',
          productName: 'Coffee',
          productAmount: 100.0,
          quantity: 1,
          unitPrice: 100.0,
          salesId: 'test_sale',
          workSpaceId: 'test_workspace',
          syncStatus: 0,
        ),
      ];

      // Calculate simple GST
      final simpleGstResult = await simpleGstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 0.0,
        settingsProvider: settingsProvider,
      );

      expect(simpleGstResult.subtotal, equals(100.0));
      expect(simpleGstResult.hasGST, isTrue);
      expect(simpleGstResult.grandTotal, greaterThan(100.0));
    });

    test('Billing provider should integrate with GST services', () async {
      // Enable tax in settings
      settingsProvider.toggleTax(true);
      
      // Add products to billing provider
      final product = SalesTransactionDto(
        id: 1,
        productId: 'product_1',
        productName: 'Coffee',
        productAmount: 100.0,
        quantity: 1,
        unitPrice: 100.0,
        salesId: 'test_sale',
        workSpaceId: 'test_workspace',
        syncStatus: 0,
      );
      
      billingProvider.addProduct(product);
      
      // Calculate GST through billing provider
      await billingProvider.calculateGST(settingsProvider);
      await billingProvider.calculateSimpleGST(settingsProvider);
      
      expect(billingProvider.gstCalculation, isNotNull);
      expect(billingProvider.simpleGstResult, isNotNull);
      expect(billingProvider.gstCalculation!.totalGST, greaterThan(0));
      expect(billingProvider.simpleGstResult!.hasGST, isTrue);
    });

    test('GST should be disabled when tax is turned off', () async {
      // Disable tax in settings
      settingsProvider.toggleTax(false);
      
      final cartItems = [
        SalesTransactionDto(
          id: 1,
          productId: 'product_1',
          productName: 'Coffee',
          productAmount: 100.0,
          quantity: 1,
          unitPrice: 100.0,
          salesId: 'test_sale',
          workSpaceId: 'test_workspace',
          syncStatus: 0,
        ),
      ];

      // Calculate GST with tax disabled
      final gstCalculation = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 0.0,
        settingsProvider: settingsProvider,
      );

      expect(gstCalculation.totalGST, equals(0.0));
      expect(gstCalculation.grandTotal, equals(100.0));
    });

    test('GST configuration should provide business information', () async {
      final dao = await database.gstConfigurationDao;
      
      // Check if GST is configured
      final isConfigured = await dao.isGSTConfigured('test_workspace');
      expect(isConfigured, isTrue);
      
      // Get business GSTIN
      final gstin = await dao.getBusinessGSTIN('test_workspace');
      expect(gstin, isNotNull);
      
      // Get default GST rate
      final defaultRate = await dao.getDefaultGSTRate('test_workspace');
      expect(defaultRate, equals(18.0));
      
      // Get configuration summary
      final summary = await dao.getConfigurationSummary('test_workspace');
      expect(summary['configured'], isTrue);
      expect(summary['businessName'], equals('Test Coffee Shop'));
    });

    test('GST calculation should handle discounts correctly', () async {
      settingsProvider.toggleTax(true);
      
      final cartItems = [
        SalesTransactionDto(
          id: 1,
          productId: 'product_1',
          productName: 'Coffee',
          productAmount: 100.0,
          quantity: 1,
          unitPrice: 100.0,
          salesId: 'test_sale',
          workSpaceId: 'test_workspace',
          syncStatus: 0,
        ),
      ];

      // Calculate GST with discount
      final gstCalculation = await gstService.calculateGST(
        cartItems: cartItems,
        discountAmount: 10.0, // 10% discount
        settingsProvider: settingsProvider,
      );

      expect(gstCalculation.subtotal, equals(100.0));
      expect(gstCalculation.discountAmount, equals(10.0));
      expect(gstCalculation.taxableAmount, equals(90.0));
      expect(gstCalculation.totalGST, greaterThan(0));
      expect(gstCalculation.grandTotal, equals(gstCalculation.taxableAmount + gstCalculation.totalGST));
    });
  });
}

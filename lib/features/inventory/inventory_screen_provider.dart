import 'dart:developer';

import 'package:coffee_cofe/database/dao/inventory_dao.dart';
import 'package:coffee_cofe/database/entities/inventory_category_dto.dart';
import 'package:coffee_cofe/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../database/app_database.dart';
import '../../widgets/common_dropdown.dart';

class InventoryScreenProvider extends ChangeNotifier {
  // List of units
  List<DropdownItem> units = [
    DropdownItem(id: 'kg', label: 'Kg'),
    DropdownItem(id: 'liter', label: 'Liter'),
    DropdownItem(id: 'no', label: 'No'),
    DropdownItem(id: 'pack', label: 'Pack'),
  ];

  DropdownItem? selectedUnit;
  DropdownItem? selectedOutputUnit;

  // You can use this to set the selected unit from dropdown
  void onChageunit(DropdownItem? value) {
    selectedUnit = value;
    notifyListeners(); // Notify UI update
  }

  void onChangeOutputUnit(DropdownItem? value) {
    selectedOutputUnit = value;
    notifyListeners();
  }

  // final List<String> units = ['Kg', 'Litres', 'No', 'Pack'];
  // String? selectedUnit;
  InventoryDto? _selectedInventory;
  InventoryDto? get selectedInventory => _selectedInventory;

  // Convertible inventory state
  bool _isConvertible = false;
  bool get isConvertible => _isConvertible;

  void setConvertible(bool value) {
    _isConvertible = value;
    if (!value) {
      // Clear conversion fields when disabled
      outputItemNameController.clear();
      outputQuantityController.clear();
      conversionRatioController.clear();
      selectedOutputUnit = null;
    }
    notifyListeners();
  }
  // List<ProductDto> _products = [];

  InventoryDao? _inventoryDAO;

  List<InventoryDto> _inventoryList = [];
  List<InventoryDto> get inventoryList => _inventoryList;

  final TextEditingController nameController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();

  // Convertible inventory controllers
  final TextEditingController outputItemNameController = TextEditingController();
  final TextEditingController outputQuantityController = TextEditingController();
  final TextEditingController conversionRatioController = TextEditingController();

  oneEdit(InventoryDto value) {
    _selectedInventory = value;
    nameController.text = _selectedInventory?.inventoyProductName ?? '';
    quantityController.text = _selectedInventory?.inventoryQunatity ?? '';
    log(selectedInventory?.inventoryUnit ?? '-');
    selectedUnit = units.firstWhereOrNull(
        (item) => item.label.toLowerCase() == _selectedInventory?.inventoryUnit?.toLowerCase());

    // Load conversion fields if item is convertible
    _isConvertible = _selectedInventory?.isConvertible ?? false;
    if (_isConvertible) {
      outputItemNameController.text = _selectedInventory?.outputItemName ?? '';
      outputQuantityController.text = _selectedInventory?.outputQuantity ?? '';
      conversionRatioController.text = _selectedInventory?.conversionRatio ?? '';
      selectedOutputUnit = units.firstWhereOrNull(
          (item) => item.label.toLowerCase() == _selectedInventory?.outputUnit?.toLowerCase());
    }

    notifyListeners();
  }

  initFunction() async {
    await _initializeDAO();
    await loadInventories();
    notifyListeners();
  }

  Future _initializeDAO() async {
    try {
      _inventoryDAO = await AppDatabase().inventoryDao;
    } catch (e) {
      // _errorMessage = 'Failed to initialize the database: $e';
      notifyListeners();
    }
  }

  bool _validateConversionFields() {
    if (!_isConvertible) return true;

    if (outputItemNameController.text.trim().isEmpty) {
      return false;
    }
    if (outputQuantityController.text.trim().isEmpty) {
      return false;
    }
    if (selectedOutputUnit == null) {
      return false;
    }
    if (conversionRatioController.text.trim().isEmpty) {
      return false;
    }

    // Validate conversion ratio is a valid number
    if (double.tryParse(conversionRatioController.text.trim()) == null) {
      return false;
    }

    return true;
  }

  Future<bool> saveCategory() async {
    try {
      // Validate conversion fields if convertible is enabled
      if (!_validateConversionFields()) {
        return false;
      }
      final inventory = InventoryDto(
        shopId: MyApp.shopId,
        inventoryId: selectedInventory?.inventoryId ?? const Uuid().v4(), // reuse ID if editing
        inventoyProductName: nameController.text.trim(),
        inventoryQunatity: (double.tryParse(quantityController.text.trim()) ?? 0.0).toString(),
        inventoryUnit: selectedUnit?.label,
        createdDate: selectedInventory?.createdDate ?? DateTime.now().toIso8601String(),
        updatedDate: DateTime.now().toIso8601String(),
        status: 1,
        rowStatus: 1,
        // Conversion fields
        isConvertible: _isConvertible,
        outputItemName: _isConvertible ? outputItemNameController.text.trim() : null,
        outputQuantity: _isConvertible ? outputQuantityController.text.trim() : null,
        outputUnit: _isConvertible ? selectedOutputUnit?.label : null,
        conversionRatio: _isConvertible ? conversionRatioController.text.trim() : null,
        convertedStock: selectedInventory?.convertedStock ?? '0',
      );

      if (selectedInventory == null) {
        // Insert new
        await _inventoryDAO?.insertInventory(inventory);
      } else {
        // Update existing
        await _inventoryDAO?.updateInventory(inventory);
      }

      await loadInventories(); // Refresh list
      await clearCategoryForm();

      return true; // success
    } catch (e) {
      await clearCategoryForm();
      log('Error saving inventory: $e');
      return false; // failed
    }
  }

  clearCategoryForm() {
    quantityController.clear();
    nameController.clear();
    _selectedInventory = null;
    selectedUnit = null;

    // Clear conversion fields
    _isConvertible = false;
    outputItemNameController.clear();
    outputQuantityController.clear();
    conversionRatioController.clear();
    selectedOutputUnit = null;

    notifyListeners();
  }

  /// Convert raw inventory to converted stock
  Future<bool> convertInventory(InventoryDto inventory, double rawAmountToConvert) async {
    try {
      if (!inventory.canConvert) return false;

      final conversionRatio = inventory.conversionRatioValue;
      final convertedAmount = rawAmountToConvert * conversionRatio;

      // Calculate new quantities
      final newRawQuantity = inventory.rawQuantity - rawAmountToConvert;
      final newConvertedQuantity = inventory.convertedQuantity + convertedAmount;

      // Update the inventory with new quantities
      final updatedInventory = InventoryDto(
        id: inventory.id,
        shopId: inventory.shopId,
        inventoryId: inventory.inventoryId,
        inventoyProductName: inventory.inventoyProductName,
        inventoryQunatity: newRawQuantity.toString(),
        inventoryUnit: inventory.inventoryUnit,
        createdDate: inventory.createdDate,
        updatedDate: DateTime.now().toIso8601String(),
        status: inventory.status,
        rowStatus: inventory.rowStatus,
        isConvertible: inventory.isConvertible,
        outputItemName: inventory.outputItemName,
        outputQuantity: inventory.outputQuantity,
        outputUnit: inventory.outputUnit,
        conversionRatio: inventory.conversionRatio,
        convertedStock: newConvertedQuantity.toString(),
      );

      // Save to database
      final success = await _inventoryDAO?.updateInventory(updatedInventory);
      if (success != null && success > 0) {
        await loadInventories(); // Refresh the list
        return true;
      }
      return false;
    } catch (e) {
      log('Error converting inventory: $e');
      return false;
    }
  }

  Future loadInventories() async {
    _inventoryList.clear();
    var list = await _inventoryDAO?.getAllInventoryList();
    _inventoryList = list ?? [];

    notifyListeners();
  }

  List<InventoryDto> filteredInventoryList(String query) {
    if (query.isEmpty) {
      return _inventoryList;
    } else {
      return _inventoryList
          .where((item) => item.inventoyProductName!.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
  }
}
